import UnoIcon from "@/components/UnoIcon";
import { Flex } from "antd";
import clsx from "clsx";
import { useSnapshot } from "valtio";
import Group from "../Group";
import List from "../List";
import Pin from "../Pin";
import Search from "../Search";

const Float = () => {
	const { search } = useSnapshot(clipboardStore);
	const { t } = useTranslation();

	return (
		<div
			className={clsx("h-screen bg-color-1", {
				"rounded-2xl": !isWin,
				"b b-color-1": isLinux,
			})}
		>
			<Flex
				data-tauri-drag-region
				vertical
				gap={12}
				className={clsx("h-full", {
					"flex-col-reverse": search.position === "bottom",
				})}
			>
				<div
					data-tauri-drag-region
					className={clsx(
						"flex cursor-pointer items-center justify-between bg-blue-500/10 px-6 py-4 dark:bg-gray-800 dark:text-gray-200",
						{
							"rounded-t-2xl": !isWin,
						},
					)}
				>
					<h1
						data-tauri-drag-region
						className="m-0 text-left font-semibold text-gray-800 text-lg tracking-wide dark:text-gray-100"
					>
						{t("preference.menu.title.clipboard")}
					</h1>
					<UnoIcon
						name="i-lucide:x"
						className="cursor-pointer text-base text-gray-500 transition-colors duration-200 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
						onClick={() => {
							hideWindow();
						}}
					/>
				</div>
				<div className="px-6 pb-2">
					<Search />
				</div>
				<Flex
					data-tauri-drag-region
					vertical
					gap={12}
					className="flex-1 overflow-hidden"
				>
					<Flex
						data-tauri-drag-region
						align="center"
						justify="space-between"
						gap="small"
						className="px-3"
					>
						<Group />

						<Flex align="center" gap={4} className="text-color-2 text-lg">
							<Pin />

							<UnoIcon
								hoverable
								name="i-lets-icons:setting-alt-line"
								onClick={() => {
									showWindow("preference");
								}}
							/>
						</Flex>
					</Flex>

					<List />
				</Flex>
			</Flex>
		</div>
	);
};

export default Float;
