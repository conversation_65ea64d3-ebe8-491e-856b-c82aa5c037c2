{"$schema": "https://schema.tauri.app/config/2.0.0", "productName": "OnePaste", "mainBinaryName": "OnePaste", "version": "../package.json", "identifier": "com.tcnum.onepaste", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"macOSPrivateApi": true, "windows": [{"label": "main", "title": "OnePaste", "url": "index.html/#/", "width": 360, "height": 600, "minWidth": 360, "minHeight": 600, "maximizable": false, "decorations": false, "visible": false, "transparent": true, "alwaysOnTop": true, "acceptFirstMouse": true, "skipTaskbar": true, "visibleOnAllWorkspaces": true}, {"label": "preference", "url": "index.html/#/preference", "width": 700, "height": 480, "minWidth": 700, "minHeight": 480, "center": true, "visible": false, "transparent": true, "maximizable": false, "hiddenTitle": true, "skipTaskbar": true, "titleBarStyle": "Overlay", "dragDropEnabled": false, "windowEffects": {"effects": ["sidebar"], "state": "active"}}], "security": {"csp": null, "dangerousDisableAssetCspModification": true, "assetProtocol": {"enable": true, "scope": {"allow": ["**/*"], "requireLiteralLeadingDot": false}}}}, "bundle": {"active": true, "createUpdaterArtifacts": false, "targets": ["app", "dmg"], "category": "Productivity", "shortDescription": "OnePaste - 跨平台剪贴板管理工具", "longDescription": "OnePaste 是一款跨平台剪贴板管理工具，支持文本、图片、文件等多种格式的剪贴板内容管理。", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["assets/tray.ico", "assets/drag-icon.png"], "macOS": {"frameworks": [], "minimumSystemVersion": "10.15", "exceptionDomain": "", "signingIdentity": "Developer ID Application: Technetium LLC (2RMYG9B3XY)", "providerShortName": null, "entitlements": "entitlements.plist", "dmg": {"appPosition": {"x": 180, "y": 170}, "applicationFolderPosition": {"x": 480, "y": 170}, "windowSize": {"width": 660, "height": 400}}}}, "plugins": {"updater": {"pubkey": "dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IDcwNEIyQkUwRjNEMTg4NgpSV1NHR0QwUHZySUVCeXFRQzZ4aEpNRWdFdThVQ2I4b2dSQktRcmNtV1dYTFJJV1h2VmVVZXBWSgo=", "endpoints": []}, "fs": {"requireLiteralLeadingDot": false}}}