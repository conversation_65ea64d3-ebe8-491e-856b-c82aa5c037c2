[package]
name = "onepaste"
version = "1.0.1"
description = "一款跨平台剪贴板管理工具"
authors = []
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "onepaste_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { workspace = true, features = ["tray-icon", "protocol-asset", "macos-private-api", "image-ico"] }
serde = { workspace = true, features = ["derive"] }
serde_json.workspace = true
tauri-plugin-shell.workspace = true
tauri-plugin-single-instance = "2"
tauri-plugin-autostart = "2"
tauri-plugin-sql = { version = "2", features = ["sqlite"] }
tauri-plugin-log = "2"
tauri-plugin-global-shortcut = "2"
tauri-plugin-os = "2"
tauri-plugin-dialog = "2"   
tauri-plugin-fs = "2"
tauri-plugin-updater = "2"
tauri-plugin-process = "2"
tauri-plugin-drag = "2"
tauri-plugin-macos-permissions = "2"
tauri-plugin-locale = "2"
tauri-plugin-opener = "2"
tauri-plugin-prevent-default = "1"
tauri-plugin-fs-pro.workspace = true
tauri-plugin-onepaste-window.workspace = true
tauri-plugin-onepaste-clipboard.workspace = true
tauri-plugin-onepaste-ocr.workspace = true
tauri-plugin-onepaste-paste.workspace = true
tauri-plugin-onepaste-autostart.workspace = true
jieba-rs = "0.7"

[target."cfg(target_os = \"macos\")".dependencies]
tauri-nspanel.workspace = true

[features]
cargo-clippy = []
