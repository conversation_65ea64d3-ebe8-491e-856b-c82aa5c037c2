[package]
name = "tauri-plugin-onepaste-paste"
version = "0.1.0"
authors = []
description = ""
edition = "2021"
rust-version = "1.77.2"
links = "tauri-plugin-onepaste-paste"

[dependencies]
tauri.workspace = true
serde.workspace = true
tauri-plugin-onepaste-window.workspace = true

[build-dependencies]
tauri-plugin.workspace = true

[target."cfg(target_os = \"macos\")".dependencies]
cocoa.workspace = true
objc = ">=0.2, <1"
core-graphics = "0.23"

[target."cfg(target_os = \"windows\")".dependencies]
log.workspace = true
winapi = { version = ">=0.3, <1", features = ["winuser", "windef"] }
enigo = ">=0.2, <1"

[target."cfg(target_os = \"linux\")".dependencies]
log.workspace = true
x11 = "2"
rdev = ">=0.5, <1"

[features]
cargo-clippy = []