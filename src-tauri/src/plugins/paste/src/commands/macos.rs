use cocoa::base::{id, nil};
use cocoa::foundation::{NSAutoreleasePool, NSString};
use core_graphics::event::{CGEvent, CGEventFlags, CGEventTapLocation};
use core_graphics::event_source::{CGEventSource, CGEventSourceStateID};
use objc::declare::ClassDecl;
use objc::runtime::{Class, Object, Sel};
use objc::{msg_send, sel, sel_impl};
use std::ffi::CStr;
use std::process::Command;
use std::sync::{Mutex, OnceLock};
use std::thread;
use tauri::{command, AppHandle, Runtime, WebviewWindow};
use tauri_plugin_onepaste_window::{set_macos_panel, MacOSPanelStatus, MAIN_WINDOW_TITLE};

static PREVIOUS_WINDOW: Mutex<Option<i32>> = Mutex::new(None);

// 缓存粘贴方法的选择，避免重复检查
static PREFERRED_PASTE_METHOD: OnceLock<PasteMethod> = OnceLock::new();

#[derive(Clone, Copy)]
enum PasteMethod {
    AppleScript,
    CGEvent,
}

extern "C" fn application_did_activate(_self: &Object, _cmd: Sel, notification: id) {
    unsafe {
        let ns_app_key = NSString::alloc(nil).init_str("NSWorkspaceApplicationKey");

        let user_info: id = msg_send![notification, userInfo];
        if user_info == nil {
            return;
        }

        let app: id = msg_send![user_info, objectForKey: ns_app_key];
        if app == nil {
            return;
        }

        let localized_name: id = msg_send![app, localizedName];
        let name_str: *const i8 = msg_send![localized_name, UTF8String];
        let name_cstr = CStr::from_ptr(name_str);
        let name = name_cstr.to_str().unwrap_or("Unknown").to_string();

        if name == MAIN_WINDOW_TITLE {
            return;
        }

        let process_id: i32 = msg_send![app, processIdentifier];

        let mut previous_window = PREVIOUS_WINDOW.lock().unwrap();
        let _ = previous_window.insert(process_id);
    }
}

// 监听窗口切换
pub fn observe_app() {
    thread::spawn(|| unsafe {
        let _pool = NSAutoreleasePool::new(nil);

        let superclass = Class::get("NSObject").unwrap();
        let mut decl = ClassDecl::new("AppObserver", superclass).unwrap();
        decl.add_method(
            sel!(applicationDidActivate:),
            application_did_activate as extern "C" fn(&Object, Sel, id),
        );
        let observer_class = decl.register();
        let observer: id = msg_send![observer_class, new];

        let workspace: id = msg_send![Class::get("NSWorkspace").unwrap(), sharedWorkspace];
        let notification_center: id = msg_send![workspace, notificationCenter];
        let ns_notification_name =
            NSString::alloc(nil).init_str("NSWorkspaceDidActivateApplicationNotification");

        let _: id = msg_send![notification_center,
            addObserver: observer
            selector: sel!(applicationDidActivate:)
            name: ns_notification_name
            object: nil
        ];

        let run_loop: id = msg_send![Class::get("NSRunLoop").unwrap(), currentRunLoop];
        let _: () = msg_send![run_loop, run];
    });
}

// 获取前一个窗口
pub fn get_previous_window() -> Option<i32> {
    return PREVIOUS_WINDOW.lock().unwrap().clone();
}

// 使用 CGEvent 进行粘贴的备用方法
fn paste_with_cgevent() -> Result<(), String> {
    let source = CGEventSource::new(CGEventSourceStateID::HIDSystemState)
        .map_err(|e| format!("创建事件源失败: {:?}", e))?;

    // V 键的键码是 9
    let v_key_code = 9u16;

    // 创建 Cmd+V 按键事件
    let cmd_v_down = CGEvent::new_keyboard_event(source.clone(), v_key_code, true) // V key down
        .map_err(|e| format!("创建按键按下事件失败: {:?}", e))?;

    let cmd_v_up = CGEvent::new_keyboard_event(source, v_key_code, false) // V key up
        .map_err(|e| format!("创建按键释放事件失败: {:?}", e))?;

    // 设置 Command 修饰键
    cmd_v_down.set_flags(CGEventFlags::CGEventFlagCommand);
    cmd_v_up.set_flags(CGEventFlags::CGEventFlagCommand);

    // 发送事件，减少延迟
    cmd_v_down.post(CGEventTapLocation::HID);
    std::thread::sleep(std::time::Duration::from_millis(1)); // 减少到 1ms
    cmd_v_up.post(CGEventTapLocation::HID);

    Ok(())
}

// 粘贴
#[command]
pub async fn paste<R: Runtime>(app_handle: AppHandle<R>, window: WebviewWindow<R>) -> Result<(), String> {
    set_macos_panel(&app_handle, &window, MacOSPanelStatus::Resign);

    // 减少等待时间，只等待 30ms 确保窗口切换
    std::thread::sleep(std::time::Duration::from_millis(30));

    // 检查是否已经确定了首选方法
    let preferred_method = PREFERRED_PASTE_METHOD.get_or_init(|| {
        // 第一次运行时快速测试哪种方法可用
        let test_script = r#"tell application "System Events" to return true"#;
        match Command::new("osascript").args(["-e", test_script]).output() {
            Ok(output) if output.status.success() => PasteMethod::AppleScript,
            _ => PasteMethod::CGEvent,
        }
    });

    match preferred_method {
        PasteMethod::AppleScript => {
            let script = r#"tell application "System Events" to keystroke "v" using command down"#;
            match Command::new("osascript").args(["-e", script]).output() {
                Ok(output) if output.status.success() => Ok(()),
                Ok(output) => {
                    let error_msg = String::from_utf8_lossy(&output.stderr);
                    if error_msg.contains("not allowed assistive access") || error_msg.contains("accessibility") {
                        Err("需要辅助功能权限。请在系统偏好设置 > 安全性与隐私 > 隐私 > 辅助功能中添加 OnePaste 应用。".to_string())
                    } else {
                        // AppleScript 失败，回退到 CGEvent
                        paste_with_cgevent()
                    }
                }
                Err(_) => paste_with_cgevent(),
            }
        }
        PasteMethod::CGEvent => paste_with_cgevent(),
    }
}

// 检查 System Events 状态
fn check_system_events_status() -> Result<(), String> {
    // 检查 System Events 是否运行
    let check_script = r#"
    tell application "System Events"
        return "running"
    end tell
    "#;

    match Command::new("osascript")
        .args(["-e", check_script])
        .output()
    {
        Ok(output) => {
            if output.status.success() {
                Ok(())
            } else {
                let error_msg = String::from_utf8_lossy(&output.stderr);
                if error_msg.contains("(-600)") || error_msg.contains("应用程序没有运行") {
                    // 尝试启动 System Events
                    let launch_script = r#"
                    tell application "System Events"
                        activate
                    end tell
                    "#;

                    match Command::new("osascript")
                        .args(["-e", launch_script])
                        .output()
                    {
                        Ok(_) => {
                            std::thread::sleep(std::time::Duration::from_millis(500));
                            Ok(())
                        }
                        Err(e) => Err(format!("无法启动 System Events: {}", e))
                    }
                } else {
                    Err(format!("System Events 检查失败: {}", error_msg))
                }
            }
        }
        Err(e) => Err(format!("无法检查 System Events: {}", e))
    }
}

// 检查辅助功能权限
#[command]
pub async fn check_accessibility_permission() -> Result<bool, String> {
    // 先检查 System Events 状态
    if let Err(e) = check_system_events_status() {
        return Err(e);
    }

    let script = r#"tell application "System Events" to return true"#;

    match Command::new("osascript")
        .args(["-e", script])
        .output()
    {
        Ok(output) => {
            if output.status.success() {
                Ok(true)
            } else {
                let error_msg = String::from_utf8_lossy(&output.stderr);
                if error_msg.contains("not allowed assistive access") || error_msg.contains("accessibility") {
                    Ok(false)
                } else {
                    Err(format!("权限检查失败: {}", error_msg))
                }
            }
        }
        Err(e) => {
            Err(format!("无法检查权限: {}", e))
        }
    }
}

// 打开系统偏好设置的辅助功能页面
#[command]
pub async fn open_accessibility_settings() -> Result<(), String> {
    fallback_open_accessibility_settings()
}

// 备用方法：使用 open 命令
fn fallback_open_accessibility_settings() -> Result<(), String> {
    // 尝试多种方法打开辅助功能设置
    let methods = vec![
        // 方法1: 直接使用 URL scheme
        "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility",
        "x-apple.systempreferences:com.apple.preference.security",
        // 方法2: 尝试新的系统设置 URL (macOS 13+)
        "x-apple.systemsettings:com.apple.preference.security?Privacy_Accessibility",
        // 方法3: 直接打开应用
        "/System/Applications/System Settings.app",
        "/System/Applications/System Preferences.app",
    ];

    for target in methods {
        let result = Command::new("open").arg(target).output();

        match result {
            Ok(output) if output.status.success() => {
                return Ok(());
            }
            _ => continue,
        }
    }

    Err("无法打开系统设置".to_string())
}
